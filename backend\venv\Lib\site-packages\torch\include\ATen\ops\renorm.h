#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/renorm_ops.h>

namespace at {


// aten::renorm.out(Tensor self, Scalar p, int dim, <PERSON><PERSON><PERSON> maxnorm, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & renorm_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) {
    return at::_ops::renorm_out::call(self, p, dim, maxnorm, out);
}
// aten::renorm.out(Tensor self, <PERSON><PERSON><PERSON> p, int dim, <PERSON>ala<PERSON> maxnorm, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & renorm_outf(const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm, at::Tensor & out) {
    return at::_ops::renorm_out::call(self, p, dim, maxnorm, out);
}

// aten::renorm(Tensor self, Scalar p, int dim, Scalar maxnorm) -> Tensor
inline at::Tensor renorm(const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) {
    return at::_ops::renorm::call(self, p, dim, maxnorm);
}

}
