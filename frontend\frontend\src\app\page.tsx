"use client";
import { useState } from "react";
import axios from "axios";
import { motion } from "framer-motion";

export default function Home() {
  const [image, setImage] = useState<File | null>(null);
  const [result, setResult] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  async function handleUpload() {
    if (!image) return;
    setLoading(true);
    const formData = new FormData();
    formData.append("file", image);
    try {
      const response = await axios.post("http://localhost:8000/predict", formData, {
        responseType: "blob",
      });
      const url = URL.createObjectURL(response.data);
      setResult(url);
    } catch (err) {
      console.error("Prediction failed", err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-indigo-800 to-black flex flex-col items-center justify-center text-white">
      <motion.h1
        className="text-4xl font-bold mb-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        PixMind AI 🧠✨
      </motion.h1>
      <input
        type="file"
        onChange={e => setImage(e.target.files?.[0] || null)}
        className="mb-4 bg-gray-800 rounded p-2"
        aria-label="Upload image"
      />
      <button
        onClick={handleUpload}
        disabled={!image || loading}
        className="px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded transition"
        aria-busy={loading}
      >
        {loading ? "Processing..." : "Generate Image"}
      </button>
      {result && (
        <motion.div className="mt-6" initial={{ scale: 0 }} animate={{ scale: 1 }}>
          <h2 className="text-xl mb-2">Result:</h2>
          <img src={result} alt="Output" className="rounded shadow-xl max-w-md" />
        </motion.div>
      )}
    </main>
  );
}
