from fastapi import Fast<PERSON><PERSON>, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from PIL import Image, ImageFilter
import torch
import torch.nn.functional as F
import numpy as np
from io import BytesIO

app = FastAPI()

# Allow frontend requests (update for deployed URL)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load model (your provided .pth file)
model = torch.load("model.pth", map_location='cpu')
model.eval()

def predict_image(model, img: Image.Image) -> Image.Image:
    image = img.filter(ImageFilter.SHARPEN)
    h, w = image.size
    img_arr = np.array(image)
    img_mod = torch.from_numpy(img_arr.astype(np.float32) / 255.0).permute(2, 0, 1)
    c, h, w = img_mod.shape

    pad_H = ((h + 255) // 256) * 256 - h
    pad_W = ((w + 255) // 256) * 256 - w
    img_mod = F.pad(img_mod, (0, pad_W, 0, pad_H), mode="reflect")

    patches = img_mod.unfold(1, 256, 256).unfold(2, 256, 256)
    patches = patches.permute(1, 2, 0, 3, 4)
    patches = patches.reshape(-1, c, 256, 256)

    with torch.no_grad():
        output_tensor = model(patches)

    N = output_tensor.shape[0]
    output_tensor = output_tensor.reshape(N, -1).T.unsqueeze(0)
    output_img = F.fold(output_tensor, output_size=(img_mod.shape[1], img_mod.shape[2]),
                        kernel_size=(256, 256), stride=(256, 256)).squeeze(0)

    output_img_clamped = output_img[:, :h, :w].clamp(0, 1)
    output_pil = Image.fromarray((output_img_clamped.permute(1, 2, 0).numpy() * 255).astype(np.uint8))
    return output_pil

@app.post("/predict")
async def predict(file: UploadFile = File(...)):
    img = Image.open(BytesIO(await file.read())).convert("RGB")
    result = predict_image(model, img)

    output_bytes = BytesIO()
    result.save(output_bytes, format="PNG")
    output_bytes.seek(0)
    return StreamingResponse(output_bytes, media_type="image/png") 