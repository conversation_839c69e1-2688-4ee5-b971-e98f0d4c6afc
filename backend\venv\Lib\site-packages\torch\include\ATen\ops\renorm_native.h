#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/renorm_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_renorm_out : public at::meta::structured_renorm {
void impl(const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm, const at::Tensor & out);
};
} // namespace native
} // namespace at
